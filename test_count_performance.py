#!/usr/bin/env python3
"""
Test script to compare count performance between different methods.
Run this script to test the performance improvements.
"""

import asyncio
import time
import requests
import json
from typing import Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8000"  # Adjust as needed
TEST_QUERIES = [
    {"term": "water"},
    {"term": "climate"},
    {"keyword": "hydrology"},
    {"contentType": "Dataset"},
    {"creatorName": "Smith"},
    {"term": "water", "keyword": "hydrology"},
]

async def test_count_performance():
    """Test different count methods and compare performance"""
    
    print("🧪 Testing Count Performance Optimizations")
    print("=" * 50)
    
    results = []
    
    for i, query in enumerate(TEST_QUERIES, 1):
        print(f"\n📊 Test {i}: {query}")
        print("-" * 30)
        
        # Test 1: Accurate count (slow)
        start_time = time.time()
        try:
            response = requests.get(
                f"{API_BASE_URL}/search/count",
                params={**query, "estimate": False, "use_cache": False}
            )
            accurate_result = response.json()
            accurate_time = time.time() - start_time
            accurate_count = accurate_result.get("count", 0)
            print(f"✅ Accurate count: {accurate_count} (took {accurate_time:.2f}s)")
        except Exception as e:
            print(f"❌ Accurate count failed: {e}")
            accurate_time = None
            accurate_count = None
        
        # Test 2: Estimated count (fast)
        start_time = time.time()
        try:
            response = requests.get(
                f"{API_BASE_URL}/search/count",
                params={**query, "estimate": True, "use_cache": False}
            )
            estimated_result = response.json()
            estimated_time = time.time() - start_time
            estimated_count = estimated_result.get("count", 0)
            is_estimate = estimated_result.get("estimate", False)
            print(f"⚡ Estimated count: {estimated_count} (took {estimated_time:.2f}s) {'[ESTIMATE]' if is_estimate else ''}")
        except Exception as e:
            print(f"❌ Estimated count failed: {e}")
            estimated_time = None
            estimated_count = None
        
        # Test 3: Cached count (should be very fast on second call)
        start_time = time.time()
        try:
            response = requests.get(
                f"{API_BASE_URL}/search/count",
                params={**query, "estimate": True, "use_cache": True}
            )
            cached_result = response.json()
            cached_time = time.time() - start_time
            cached_count = cached_result.get("count", 0)
            print(f"🚀 Cached count: {cached_count} (took {cached_time:.2f}s)")
        except Exception as e:
            print(f"❌ Cached count failed: {e}")
            cached_time = None
            cached_count = None
        
        # Test 4: Combined search + count
        start_time = time.time()
        try:
            response = requests.get(
                f"{API_BASE_URL}/search",
                params={**query, "include_count": True, "pageSize": 10}
            )
            combined_result = response.json()
            combined_time = time.time() - start_time
            combined_count = combined_result.get("meta", {}).get("count", {}).get("count", 0)
            docs_count = len(combined_result.get("docs", []))
            print(f"🔄 Combined search+count: {combined_count} total, {docs_count} docs (took {combined_time:.2f}s)")
        except Exception as e:
            print(f"❌ Combined search+count failed: {e}")
            combined_time = None
            combined_count = None
        
        # Calculate performance improvements
        if accurate_time and estimated_time:
            speedup = accurate_time / estimated_time
            print(f"📈 Speedup (estimate vs accurate): {speedup:.1f}x faster")
        
        if estimated_time and cached_time:
            cache_speedup = estimated_time / cached_time
            print(f"💾 Cache speedup: {cache_speedup:.1f}x faster")
        
        results.append({
            "query": query,
            "accurate_time": accurate_time,
            "accurate_count": accurate_count,
            "estimated_time": estimated_time,
            "estimated_count": estimated_count,
            "cached_time": cached_time,
            "cached_count": cached_count,
            "combined_time": combined_time,
            "combined_count": combined_count,
        })
    
    # Summary
    print("\n📋 Performance Summary")
    print("=" * 50)
    
    avg_accurate_time = sum(r["accurate_time"] for r in results if r["accurate_time"]) / len([r for r in results if r["accurate_time"]])
    avg_estimated_time = sum(r["estimated_time"] for r in results if r["estimated_time"]) / len([r for r in results if r["estimated_time"]])
    avg_cached_time = sum(r["cached_time"] for r in results if r["cached_time"]) / len([r for r in results if r["cached_time"]])
    
    print(f"Average accurate count time: {avg_accurate_time:.2f}s")
    print(f"Average estimated count time: {avg_estimated_time:.2f}s")
    print(f"Average cached count time: {avg_cached_time:.2f}s")
    print(f"Overall speedup (estimate): {avg_accurate_time / avg_estimated_time:.1f}x")
    print(f"Overall speedup (cache): {avg_estimated_time / avg_cached_time:.1f}x")
    
    # Test cache stats
    try:
        response = requests.get(f"{API_BASE_URL}/search/count/cache/stats")
        cache_stats = response.json()
        print(f"\n💾 Cache Stats:")
        print(f"Cache size: {cache_stats.get('cache_size', 0)}")
        print(f"Max cache size: {cache_stats.get('max_cache_size', 0)}")
    except Exception as e:
        print(f"❌ Cache stats failed: {e}")

def test_cache_management():
    """Test cache management endpoints"""
    print("\n🧹 Testing Cache Management")
    print("=" * 30)
    
    # Clear cache
    try:
        response = requests.post(f"{API_BASE_URL}/search/count/cache/clear")
        result = response.json()
        print(f"✅ Cache cleared: {result}")
    except Exception as e:
        print(f"❌ Cache clear failed: {e}")
    
    # Check stats after clear
    try:
        response = requests.get(f"{API_BASE_URL}/search/count/cache/stats")
        stats = response.json()
        print(f"📊 Cache stats after clear: {stats}")
    except Exception as e:
        print(f"❌ Cache stats failed: {e}")

if __name__ == "__main__":
    print("🚀 Starting Count Performance Tests")
    print("Make sure your API server is running at", API_BASE_URL)
    print()
    
    # Test cache management first
    test_cache_management()
    
    # Run performance tests
    asyncio.run(test_count_performance())
    
    print("\n✅ Tests completed!")
    print("\n💡 Recommendations:")
    print("1. Use estimate=True for fast approximate counts")
    print("2. Enable caching for repeated queries")
    print("3. Use include_count=True in search for single-request results")
    print("4. Clear cache periodically if data changes frequently")
