import json
import mimetypes
import hashlib
from datetime import datetime
from typing import Optional
from functools import lru_cache

from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel, ValidationInfo, field_validator, model_validator

from config import get_settings

router = APIRouter()

# Simple in-memory cache for count results
# In production, consider using Redis or similar
_count_cache = {}
_cache_max_size = 1000


class SearchQuery(BaseModel):
    term: Optional[str] = None
    sortBy: Optional[str] = None
    reverseSort: bool = True
    contentType: Optional[str] = None
    providerName: Optional[str] = None
    creatorName: Optional[str] = None
    contributorName: Optional[str] = None
    keyword: Optional[str] = None
    dataCoverageStart: Optional[int] = None
    dataCoverageEnd: Optional[int] = None
    publishedStart: Optional[int] = None
    publishedEnd: Optional[int] = None
    dateCreatedStart: Optional[int] = None
    dateCreatedEnd: Optional[int] = None
    dateModifiedStart: Optional[int] = None
    dateModifiedEnd: Optional[int] = None
    hasPartName: Optional[str] = None
    isPartOfName: Optional[str] = None
    associatedMediaName: Optional[str] = None
    fundingGrantName: Optional[str] = None
    fundingFunderName: Optional[str] = None
    creativeWorkStatus: Optional[str] = None
    pageNumber: int = 1
    pageSize: int = 30

    @field_validator('*')
    def empty_str_to_none(cls, v, info: ValidationInfo):
        if info.field_name == 'term' and v:
            return v.strip()

        if isinstance(v, str) and v.strip() == '':
            return None
        return v

    @field_validator('dataCoverageStart', 'dataCoverageEnd', 'publishedStart', 'publishedEnd', 'dateCreatedStart', 'dateCreatedEnd', 'dateModifiedStart', 'dateModifiedEnd')
    def validate_year(cls, v, info: ValidationInfo):
        if v is None:
            return v
        try:
            datetime(v, 1, 1)
        except ValueError:
            raise ValueError(f'{info.field_name} is not a valid year')

        return v

    @model_validator(mode='after')
    def validate_date_range(self):
        if self.dataCoverageStart and self.dataCoverageEnd and self.dataCoverageEnd < self.dataCoverageStart:
            raise ValueError('dataCoverageEnd must be greater or equal to dataCoverageStart')
        if self.publishedStart and self.publishedEnd and self.publishedEnd < self.publishedStart:
            raise ValueError('publishedEnd must be greater or equal to publishedStart')
        if self.dateCreatedStart and self.dateCreatedEnd and self.dateCreatedEnd < self.dateCreatedStart:
            raise ValueError('dateCreatedEnd must be greater or equal to dateCreatedStart')
        if self.dateModifiedStart and self.dateModifiedEnd and self.dateModifiedEnd < self.dateModifiedStart:
            raise ValueError('dateModifiedEnd must be greater or equal to dateModifiedStart')

    @field_validator('pageNumber', 'pageSize')
    def validate_page(cls, v, info: ValidationInfo):
        if v <= 0:
            raise ValueError(f'{info.field_name} must be greater than 0')
        return v

    @property
    def _filters(self):
        filters = []
        if self.publishedStart:
            filters.append(
                {
                    'range': {
                        'path': 'datePublished',
                        'gte': datetime(self.publishedStart, 1, 1),
                    },
                }
            )
        if self.publishedEnd:
            filters.append(
                {
                    'range': {
                        'path': 'datePublished',
                        'lt': datetime(self.publishedEnd + 1, 1, 1),  # +1 to include all of the publishedEnd year
                    },
                }
            )

        if self.dateCreatedStart:
            filters.append(
                {
                    'range': {
                        'path': 'dateCreated',
                        'gte': datetime(self.dateCreatedStart, 1, 1),
                    },
                }
            )
        if self.dateCreatedEnd:
            filters.append(
                {
                    'range': {
                        'path': 'dateCreated',
                        'lt': datetime(self.dateCreatedEnd + 1, 1, 1),  # +1 to include all of the dateCreatedEnd year
                    },
                }
            )

        if self.dateModifiedStart:
            filters.append(
                {
                    'range': {
                        'path': 'dateModified',
                        'gte': datetime(self.dateModifiedStart, 1, 1),
                    },
                }
            )
        if self.dateModifiedEnd:
            filters.append(
                {
                    'range': {
                        'path': 'dateModified',
                        'lt': datetime(self.dateModifiedEnd + 1, 1, 1),  # +1 to include all of the dateModifiedEnd year
                    },
                }
            )

        if self.dataCoverageStart:
            filters.append(
                {'range': {'path': 'temporalCoverage.startDate', 'gte': datetime(self.dataCoverageStart, 1, 1)}}
            )
        if self.dataCoverageEnd:
            filters.append(
                {'range': {'path': 'temporalCoverage.endDate', 'lt': datetime(self.dataCoverageEnd + 1, 1, 1)}}
            )
        return filters

    @property
    def _should(self):
        search_paths = ['name', 'description', 'keywords']
        should = [{'autocomplete': {'query': self.term, 'path': key, 'fuzzy': {'maxEdits': 1}}} for key in search_paths]
        return should

    @property
    def _must(self):
        must = []
        must.append({'term': {'path': 'type', 'query': "Dataset"}})
        if self.contentType:
            must.append({'term': {'path': 'additionalType', 'query': self.contentType}})
        if self.creatorName:
            must.append({'text': {'path': 'creator.name', 'query': self.creatorName}})
        if self.contributorName:
            must.append({'text': {'path': 'contributor.name', 'query': self.contributorName}})
        if self.keyword:
            must.append({'text': {'path': 'keywords', 'query': self.keyword}})
        if self.providerName:
            must.append({'text': {'path': 'provider.name', 'query': self.providerName}})
        if self.hasPartName:
            must.append({'text': {'path': 'hasPart.name', 'query': self.hasPartName}})
        if self.isPartOfName:
            must.append({'text': {'path': 'isPartOf.name', 'query': self.isPartOfName}})
        if self.associatedMediaName:
            must.append({'text': {'path': 'associatedMedia.name', 'query': self.associatedMediaName}})
        if self.fundingGrantName:
            must.append({'text': {'path': 'funding.name', 'query': self.fundingGrantName}})
        if self.fundingFunderName:
            must.append({'text': {'path': 'funding.funder.name', 'query': self.fundingFunderName}})
        if self.creativeWorkStatus:
            must.append(
                {'text': {'path': ['creativeWorkStatus', 'creativeWorkStatus.name'], 'query': self.creativeWorkStatus}}
            )

        return must

    @property
    def stages(self):
        highlightPaths = ['name', 'description', 'keywords']
        stages = []
        compound = {'filter': self._filters, 'must': self._must}

        if self.term:
            compound['should'] = self._should

        search_stage = {
            '$search': {
                'index': 'fuzzy_search',
                'compound': compound,
            }
        }
        if self.term:
            search_stage["$search"]['highlight'] = {'path': highlightPaths}

        stages.append(search_stage)

        # sorting needs to happen before pagination
        if self.sortBy:
            if self.sortBy == "name":
                self.sortBy = "name_for_sorting"
                self.reverseSort = not self.reverseSort
            stages.append({'$sort': {self.sortBy: -1 if self.reverseSort else 1}})

        stages.append(
            {'$set': {'score': {'$meta': 'searchScore'}, 'highlights': {'$meta': 'searchHighlights'}}},
        )
        return stages

    @property
    def stages_v2(self):
        highlightPaths = ['name', 'description', 'keywords']
        searchPaths = ['name', 'description', 'keywords']
        stages = []
        compound = {'filter': self._filters, 'must': self._must}

        if self.term:
            compound['should'] = [{'autocomplete': {'query': self.term, 'path': key, 'fuzzy': {'maxEdits': 1}}} for key in searchPaths]

        search_stage = {
            '$search': {
                'index': 'fuzzy_search',
                'compound': compound,
            }
        }

        if self.term:
            search_stage["$search"]['highlight'] = {'path': highlightPaths}

        stages.append(search_stage)

        # sorting needs to happen before pagination
        # if self.sortBy:
        #     if self.sortBy == "name":
        #         self.sortBy = "name_for_sorting"
        #         self.reverseSort = not self.reverseSort
        #     stages.append({'$sort': {self.sortBy: -1 if self.reverseSort else 1}})

        stages.append(
            {'$set': {'score': {'$meta': 'searchScore'}, 'highlights': {'$meta': 'searchHighlights'}}},
        )

        if self.term:
            # get only results which meet minimum relevance score threshold
            stages.append({'$match': {'score': {'$gt': get_settings().search_relevance_score_threshold}}})

        return stages


@router.get("/search")
async def search(request: Request, search_query: SearchQuery = Depends(), include_count: bool = False):
    """
    Search for datasets.

    Args:
        include_count: If True, includes count in the response (adds some latency)
    """
    print(">> In search endpoint with search_query.stages_v2")
    result = await aggregate_stages(request, search_query.stages_v2, search_query.pageNumber, search_query.pageSize)

    if include_count:
        # Get count using fast estimation
        count_result = await aggregate_count(request, search_query.stages_v2, use_estimate=True)
        result["meta"]["count"] = count_result

    json_str = json.dumps(result, default=str)
    return json.loads(json_str)


async def aggregate_stages(request, stages, pageNumber=1, pageSize=30):
    # Insert pagination stages
    stages.append({"$skip": (pageNumber - 1) * pageSize})
    stages.append({"$limit": pageSize})

    aggregation = await request.app.mongodb["discovery"].aggregate(stages).to_list(None)

    return {"docs": aggregation, "meta": {"count": "pending"}}

@router.get("/search/count")
async def count(request: Request, search_query: SearchQuery = Depends(), estimate: bool = True, use_cache: bool = True):
    """
    Get count of search results.

    Args:
        estimate: If True, uses fast $searchMeta estimation (default).
                 If False, uses accurate but slower $count.
        use_cache: If True, uses cached results when available (default).
    """
    result = await aggregate_count(request, search_query.stages_v2, use_estimate=estimate, use_cache=use_cache)
    return result

@router.post("/search/count/cache/clear")
async def clear_count_cache():
    """Clear the count cache"""
    global _count_cache
    _count_cache.clear()
    return {"message": "Count cache cleared", "cache_size": 0}

@router.get("/search/count/cache/stats")
async def count_cache_stats():
    """Get count cache statistics"""
    return {
        "cache_size": len(_count_cache),
        "max_cache_size": _cache_max_size,
        "cache_keys": list(_count_cache.keys())[:10]  # Show first 10 keys
    }

def _get_cache_key(stages, use_estimate=True):
    """Generate a cache key from the aggregation stages"""
    stages_str = json.dumps(stages, sort_keys=True, default=str)
    cache_key = hashlib.md5(f"{stages_str}_{use_estimate}".encode()).hexdigest()
    return cache_key

def _get_from_cache(cache_key):
    """Get result from cache if available"""
    return _count_cache.get(cache_key)

def _set_cache(cache_key, result):
    """Set result in cache with simple LRU eviction"""
    if len(_count_cache) >= _cache_max_size:
        # Simple LRU: remove oldest entry
        oldest_key = next(iter(_count_cache))
        del _count_cache[oldest_key]
    _count_cache[cache_key] = result

async def aggregate_count(request, stages, use_estimate=True, use_cache=True):
    db = request.app.mongodb["discovery"]

    # Check cache first
    if use_cache:
        cache_key = _get_cache_key(stages, use_estimate)
        cached_result = _get_from_cache(cache_key)
        if cached_result is not None:
            return cached_result

    if use_estimate:
        # Use $searchMeta for fast approximate count
        result = await aggregate_count_estimate(request, stages)
    else:
        # Use $count for accurate but slower counting
        count_pipeline = list(stages)
        count_pipeline.append({"$count": "count"})

        count_result = await db.aggregate(count_pipeline).to_list(length=1)
        total_count = count_result[0]["count"] if count_result else 0
        result = {"count": total_count}

    # Cache the result
    if use_cache:
        _set_cache(cache_key, result)

    return result

async def aggregate_count_estimate(request, stages):
    """Fast count estimation using $searchMeta"""
    db = request.app.mongodb["discovery"]

    # Create a new pipeline with $searchMeta instead of $search
    if not stages or '$search' not in stages[0]:
        # Fallback to exact count if no search stage
        return await aggregate_count(request, stages, use_estimate=False)

    search_stage = stages[0]['$search'].copy()

    # Replace $search with $searchMeta
    meta_pipeline = [{
        "$searchMeta": {
            **search_stage,
            "count": {
                "type": "lowerBound"
            }
        }
    }]

    count_result = await db.aggregate(meta_pipeline).to_list(length=1)

    if count_result and 'count' in count_result[0]:
        # $searchMeta returns count info in a different structure
        count_info = count_result[0]['count']
        if 'lowerBound' in count_info:
            return {"count": count_info['lowerBound'], "estimate": True}
        elif 'total' in count_info:
            return {"count": count_info['total'], "estimate": True}

    # Fallback to exact count if estimation fails
    return await aggregate_count(request, stages, use_estimate=False)

@router.get("/typeahead")
async def typeahead(request: Request, term: str, pageSize: int = 30):
    search_paths = ['name', 'description', 'keywords']
    should = [{'autocomplete': {'query': term, 'path': key, 'fuzzy': {'maxEdits': 1}}} for key in search_paths]

    stages = [
        {
            '$search': {
                'index': 'fuzzy_search',
                'compound': {'should': should},
                'highlight': {'path': ['description', 'name', 'keywords']},
            }
        },
        {
            '$project': {
                'name': 1,
                'description': 1,
                'keywords': 1,
                'highlights': {'$meta': 'searchHighlights'},
                '_id': 0,
            }
        },
    ]
    result = await request.app.mongodb["discovery"].aggregate(stages).to_list(pageSize)
    return result


def to_associated_media(file):
    mime_type = mimetypes.guess_type(file.name)[0]
    extension = file.extension
    mime_type = mime_type if mime_type else extension
    size = 0
    return {
        "@type": "DataDownload",
        "name": file.name,
        "contentUrl": file.url,
        "contentSize": size,
        "sha256": file.checksum,
        "encodingFormat": mime_type,
    }
